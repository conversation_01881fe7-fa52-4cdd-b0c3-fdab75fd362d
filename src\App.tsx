import { useState } from "react";
import Sidebar from "./components/Sidebar";

import "bootstrap/dist/css/bootstrap.min.css";
import { floor } from "mathjs";
function App() {
  const [amount, setAmount] = useState(0);

  const [upgrade1, setUpgrade1] = useState(1);
  const [upgrade2, setUpgrade2] = useState(1);
  const [upgrade3, setUpgrade3] = useState(1);

  return (
    <div style={{ marginLeft: "20px", marginTop: "20px" }}>
      <h1> Click Times: {amount.toFixed(1)}</h1>

      <button
        className={"btn btn-primary"}
        onClick={() =>
          setAmount(amount + floor(10 * upgrade1 * upgrade2 * upgrade3) / 10)
        }
      >
        Click
      </button>

      <Sidebar
        amount={amount}
        setAmount={setAmount}
        upgrade1={upgrade1}
        setUpgrade1={setUpgrade1}
        upgrade2={upgrade2}
        setUpgrade2={setUpgrade2}
        upgrade3={upgrade3}
        setUpgrade3={setUpgrade3}
      />
    </div>
  );
}

export default App;
