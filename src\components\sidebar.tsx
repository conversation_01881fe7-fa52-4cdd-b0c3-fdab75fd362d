import { useState } from "react";
import { floor } from "mathjs";

import "bootstrap/dist/css/bootstrap.min.css";

type SidebarProps = {
  amount: number;
  setAmount: (amount: number) => void;
  upgrade1: number;
  setUpgrade1: (upgrade1: number) => void;
  upgrade2: number;
  setUpgrade2: (upgrade2: number) => void;
  upgrade3: number;
  setUpgrade3: (upgrade3: number) => void;
};

function Sidebar({
  amount,
  setAmount,
  upgrade1,
  setUpgrade1,
  upgrade2,
  setUpgrade2,
  upgrade3,
  setUpgrade3,
}: SidebarProps) {
  const [cost1, setCost1] = useState(10);
  const [cost2, setCost2] = useState(200);
  const [cost3, setCost3] = useState(3000);

  const upgrades = [
    {
      name: "Upgrade 1",
      cost: cost1,
      setCost: setCost1,
      effect: `Multiplier: x${upgrade1}`,
      upgrade: setUpgrade1,
    },
    {
      name: "Upgrade 2",
      cost: cost2,
      setCost: setCost2,
      effect: `Multiplier: x${upgrade2}`,
      upgrade: setUpgrade2,
    },
    {
      name: "Upgrade 3",
      cost: cost3,
      setCost: setCost3,
      effect: `Multiplier: x${upgrade3}`,
      upgrade: setUpgrade3,
    },
  ];

  const handleUpgrade = ({ cost, setCost, upgrade }: any) => {
    if (cost <= amount) {
      setAmount(amount - cost);
      setCost(floor(cost * 1.2));
      upgrade((prev: number) => floor(prev * 1.3));
    }
  };

  return (
    <div
      className="d-flex flex-column bg-light p-3"
      style={{ width: "250px", height: "100vh", position: "fixed" }}
    >
      <h1 className="mb-4">Upgrades</h1>
      <ul className="list-unstyled">
        {upgrades.map((upgrade) => (
          <li key={upgrade.name} className="mb-3">
            <button
              className="btn btn-primary w-100 text-start"
              onClick={() => handleUpgrade(upgrade)}
            >
              {upgrade.name}
            </button>
            <p className="mt-2 mb-1">Cost: {upgrade.cost}</p>
            <p className="text-muted">{upgrade.effect}</p>
          </li>
        ))}
      </ul>
    </div>
  );
}

export default Sidebar;
